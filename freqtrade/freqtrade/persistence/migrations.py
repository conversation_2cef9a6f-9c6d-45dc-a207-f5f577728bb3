# Override check_migrate to create all tables if missing
from sqlalchemy import inspect
from sqlalchemy import create_engine
from .models import ModelBase

def check_migrate(engine, decl_base: ModelBase, previous_tables: list):
    inspector = inspect(engine)
    tables = inspector.get_table_names()
    missing = set(decl_base.metadata.tables.keys()) - set(tables)
    if missing:
        decl_base.metadata.create_all(engine)
    # No-op for migrations beyond schema creation