#!/bin/sh
set -e

echo "[ENTRYPOINT] Starting FreqTrade container initialization..."
echo "[ENTRYPOINT] Database URL: $DB_URL"

# Enhanced database initialization with comprehensive error handling
python3 - << 'PYCODE'
import os
import time
import sys
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.exc import OperationalError as SAOperationalError
import sqlite3

def ensure_database_tables(db_url, max_retries=5, retry_delay=2):
    """
    Ensure all FreqTrade database tables exist with aggressive retry logic
    """
    print(f"[ENTRYPOINT] Ensuring database tables exist for: {db_url}")

    for attempt in range(max_retries):
        try:
            print(f"[ENTRYPOINT] Database initialization attempt {attempt + 1}/{max_retries}")

            # Import FreqTrade modules
            from freqtrade.persistence.models import init_db, ModelBase
            from freqtrade.persistence import Trade
            from freqtrade.persistence.key_value_store import _KeyValueStoreModel

            # Initialize database using FreqTrade's init_db
            print("[ENTRYPOINT] Calling FreqTrade init_db...")
            init_db(db_url)

            # Create engine to verify tables
            engine = create_engine(db_url)
            inspector = inspect(engine)
            existing_tables = inspector.get_table_names()

            print(f"[ENTRYPOINT] Existing tables: {existing_tables}")

            # Get all required tables from FreqTrade models
            required_tables = set(ModelBase.metadata.tables.keys())
            print(f"[ENTRYPOINT] Required tables: {required_tables}")

            missing_tables = required_tables - set(existing_tables)

            if missing_tables:
                print(f"[ENTRYPOINT] Missing tables detected: {missing_tables}")
                print("[ENTRYPOINT] Creating missing tables...")

                # Force create all tables
                ModelBase.metadata.create_all(engine)

                # Verify tables were created
                updated_tables = inspector.get_table_names()
                print(f"[ENTRYPOINT] Tables after creation: {updated_tables}")

                still_missing = required_tables - set(updated_tables)
                if still_missing:
                    print(f"[ENTRYPOINT] WARNING: Still missing tables: {still_missing}")
                else:
                    print("[ENTRYPOINT] ✓ All required tables now exist")
            else:
                print("[ENTRYPOINT] ✓ All required tables already exist")

            # Test basic operations on critical tables
            try:
                # Test KeyValueStore table
                with engine.connect() as conn:
                    result = conn.execute(text("SELECT COUNT(*) FROM KeyValueStore"))
                    count = result.scalar()
                    print(f"[ENTRYPOINT] KeyValueStore table test: {count} rows")

                    # Test trades table
                    result = conn.execute(text("SELECT COUNT(*) FROM trades"))
                    count = result.scalar()
                    print(f"[ENTRYPOINT] trades table test: {count} rows")

                print("[ENTRYPOINT] ✓ Database table tests passed")
                return True

            except Exception as test_e:
                print(f"[ENTRYPOINT] Database table test failed: {test_e}")
                if attempt < max_retries - 1:
                    print(f"[ENTRYPOINT] Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    continue
                else:
                    print("[ENTRYPOINT] WARNING: Database tests failed, but continuing...")
                    return False

        except Exception as e:
            print(f"[ENTRYPOINT] Database initialization failed (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print(f"[ENTRYPOINT] Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("[ENTRYPOINT] CRITICAL: All database initialization attempts failed!")
                return False

    return False

# Ensure database tables exist with aggressive retry
success = ensure_database_tables(os.environ['DB_URL'])
if success:
    print("[ENTRYPOINT] CRITICAL: ✓ DATABASE READY - FreqTrade should start successfully")
else:
    print("[ENTRYPOINT] CRITICAL: ⚠ DATABASE NOT READY - FreqTrade may encounter errors")
PYCODE

echo "[ENTRYPOINT] Starting FreqTrade trading..."
exec freqtrade trade --config /freqtrade/config.json --db-url "$DB_URL"
