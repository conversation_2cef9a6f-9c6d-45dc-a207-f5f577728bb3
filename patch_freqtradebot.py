#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to patch the freqtradebot.py file with a safe startup_backpopulate_precision function
"""

import re

def patch_freqtradebot():
    # Read the original file
    with open('freqtradebot_original.py', 'r') as f:
        content = f.read()
    
    # Define the new safe function
    new_function = '''    def startup_backpopulate_precision(self) -> None:
        """
        PATCHED VERSION: Safe startup_backpopulate_precision that handles missing database tables
        """
        try:
            from sqlite3 import OperationalError as SQLOpErr
            from sqlalchemy.exc import OperationalError as SAOpErr
            
            print('FREQTRADEBOT PATCH: startup_backpopulate_precision: Starting with error handling')
            
            # Try to get trades, but handle missing table gracefully
            try:
                trades = Trade.get_trades([Trade.contract_size.is_(None)])
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Found {len(trades)} trades to process')
            except (<PERSON><PERSON><PERSON><PERSON>Err, SAOpErr) as e:
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Database error (skipping): {e}')
                # If the trades table doesn't exist, just skip this function
                return
            except Exception as e:
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Unexpected error (skipping): {e}')
                return
            
            # Process trades if we got them successfully
            for trade in trades:
                try:
                    if trade.exchange != self.exchange.id:
                        continue
                    trade.precision_mode = self.exchange.precisionMode
                    trade.precision_mode_price = self.exchange.precision_mode_price
                    trade.amount_precision = self.exchange.get_precision_amount(trade.pair)
                    trade.price_precision = self.exchange.get_precision_price(trade.pair)
                    trade.contract_size = self.exchange.get_contract_size(trade.pair)
                except Exception as trade_e:
                    print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Error processing trade {trade}: {trade_e}')
                    continue
            
            # Try to commit changes
            try:
                Trade.commit()
                print('FREQTRADEBOT PATCH: startup_backpopulate_precision: Successfully committed changes')
            except (SQLOpErr, SAOpErr) as e:
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Commit failed (ignoring): {e}')
            except Exception as e:
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Unexpected commit error (ignoring): {e}')
                
        except Exception as e:
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Critical error (ignoring): {e}')
            # If anything goes wrong, just skip this function entirely
            return'''
    
    # Find and replace the original function
    # Pattern to match the function definition and its body
    pattern = r'    def startup_backpopulate_precision\(self\) -> None:\s*\n.*?(?=\n    def |\n\n    def |\Z)'
    
    # Replace the function
    new_content = re.sub(pattern, new_function, content, flags=re.DOTALL)
    
    # Write the patched file
    with open('freqtradebot_patched.py', 'w') as f:
        f.write(new_content)
    
    print("Successfully patched freqtradebot.py")
    print("Original function replaced with safe version that handles missing database tables")

if __name__ == "__main__":
    patch_freqtradebot()
