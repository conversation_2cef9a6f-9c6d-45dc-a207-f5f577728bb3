const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const fs = require('fs-extra'); // Use fs-extra for ensureDir etc.
const path = require('path');
const dotenv = require('dotenv');
const { formatDbUrl } = require('./lib/urlFormatter');
const { URL } = require('url');
const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const fetch = require('node-fetch'); // Added for FreqTrade API proxy functionality

// Load environment variables from the bot-manager/.env file
dotenv.config({ path: path.join(__dirname, '.env') });
// Turso CLI command (allow overriding via env if path differs)
const TURSO_CMD = process.env.TURSO_CMD || 'turso';
// Log which Turso CLI binary will be used
console.log(`Using TURSO_CMD: ${TURSO_CMD}`);
// Turso configuration: API key, organization, and region for remote SQLite DB
const TURSO_API_KEY = process.env.TURSO_API_KEY;
const TURSO_ORG = process.env.TURSO_ORG;
const TURSO_REGION = process.env.TURSO_REGION || 'us-east-1';

// Global variable for Firebase initialization status
let firebaseInitialized = false;

// Initialize Firebase Admin SDK with service account
try {
  const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');
  if (fs.existsSync(serviceAccountPath)) {
    // Load the service account file directly instead of using require
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    console.log("Firebase Admin SDK initialized with service account");
    firebaseInitialized = true;
  } else {
    console.warn("Service account file not found at:", serviceAccountPath);
    firebaseInitialized = false;
  }
} catch (error) {
  console.error("Failed to initialize Firebase Admin SDK:", error);
  firebaseInitialized = false;
}

// Now import authentication middlewares after Firebase initialization
const { authenticateToken, authorize, checkInstanceOwnership } = require('./auth');

// --- Constants ---
const PORT = process.env.PORT || 3001;
const BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, '..', 'freqtrade-instances'); // Base dir for individual bot instances
const FREQTRADE_IMAGE = process.env.FREQTRADE_IMAGE || 'freqtrade-patched:latest'; // Use our patched FreqTrade image
console.log(`Using FREQTRADE_IMAGE: ${FREQTRADE_IMAGE}`);
// Shared strategies dir (used ONLY for fallback default strategy creation if main source is empty/missing)
const STRATEGIES_DIR = process.env.STRATEGIES_DIR || path.join(__dirname, 'freqtrade-shared', 'strategies');
// Main source directory on HOST where strategies are copied FROM during provisioning
const MAIN_STRATEGIES_SOURCE_DIR = process.env.MAIN_STRATEGIES_SOURCE_DIR || '/root/freqtrade/user_data/strategies/';
// SHARED data directory on HOST where historical data resides (must be managed separately)
const SHARED_DATA_DIR = process.env.SHARED_DATA_DIR || '/root/freqtrade-shared'; // All bots will read data from subdirs here

// --- Queue System for Provisioning ---
const provisioningQueue = [];
let isProvisioning = false;

// --- Create Express App ---
const app = express();

// --- Middleware ---
// Apply Helmet security headers
app.use(helmet());

// Configure global rate limiter
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per 15-minute window
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    success: false,
    message: "Too many requests, please try again later."
  }
});

// Apply global rate limiter to all requests
app.use(globalLimiter);

// Additional strict rate limiter for token verification endpoint
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 verify attempts per 15-minute window
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: "Too many token verification attempts, please try again later."
  }
});

// Implement proper CORS with allowed origins from environment variable
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',')
  : ['http://localhost:3000'];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, Postman, etc)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS policy violation attempt from origin: ${origin}`);
      callback(new Error('CORS policy violation'), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json()); // Parse JSON request bodies

// --- Ensure Base Directories Exist ---
(async () => {
  try {
    console.log(`Ensuring bot instance base directory exists: ${BOT_BASE_DIR}`);
    await fs.ensureDir(BOT_BASE_DIR);
    console.log(`Ensuring shared strategies directory exists (for fallback): ${STRATEGIES_DIR}`);
    await fs.ensureDir(STRATEGIES_DIR);
    console.log(`Checking main strategies source directory: ${MAIN_STRATEGIES_SOURCE_DIR}`);
    if (!await fs.pathExists(MAIN_STRATEGIES_SOURCE_DIR)) {
      console.warn(`WARNING: Main strategies source directory (${MAIN_STRATEGIES_SOURCE_DIR}) does not exist.`);
    } else { console.log(`Main strategies source directory found.`); }
    console.log(`Ensuring base shared data directory exists: ${SHARED_DATA_DIR}`);
    await fs.ensureDir(SHARED_DATA_DIR); // Create the base shared data dir if it doesn't exist
    console.log("Base directories ensured/checked.");

    // Create fallback default strategy in SHARED strategy dir if it's empty
    const sharedStrategyFiles = await fs.readdir(STRATEGIES_DIR);
    if (sharedStrategyFiles.filter(f => f.endsWith('.py')).length === 0) {
      const dummyStrategyPath = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
      const dummyStrategyContent = `
import talib.abstract as ta
from pandas import DataFrame # Ensure DataFrame is imported
from freqtrade.strategy import IStrategy, IntParameter
import freqtrade.vendor.qtpylib.indicators as qtpylib
class DefaultStrategy(IStrategy):
    INTERFACE_VERSION = 3; minimal_roi = {"0": 0.01}; stoploss = -0.10; timeframe = '5m'
    process_only_new_candles = True; startup_candle_count: int = 20; use_exit_signal = True; exit_profit_only = False
    buy_rsi = IntParameter(10, 40, default=30, space='buy'); sell_rsi = IntParameter(60, 90, default=70, space='sell')
    def populate_indicators(self, df: DataFrame, md: dict) -> DataFrame: df['rsi'] = ta.RSI(df); return df
    def populate_entry_trend(self, df: DataFrame, md: dict) -> DataFrame: df.loc[(qtpylib.crossed_below(df['rsi'], self.buy_rsi.value)), 'enter_long'] = 1; return df
    def populate_exit_trend(self, df: DataFrame, md: dict) -> DataFrame: df.loc[(qtpylib.crossed_above(df['rsi'], self.sell_rsi.value)), 'exit_long'] = 1; return df
`;
      if (!await fs.pathExists(dummyStrategyPath)) { await fs.writeFile(dummyStrategyPath, dummyStrategyContent); console.log(`Created fallback DefaultStrategy: ${dummyStrategyPath}`); }
    }
  } catch (err) { console.error("FATAL: Directory setup failed.", err); process.exit(1); }
})();


// --- Process the Provisioning Queue (Using Strategy Copy & CORRECTED Shared Data Volume) ---
async function processProvisioningQueue() {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [Queue Processor] processProvisioningQueue called. isProvisioning: ${isProvisioning}, Queue length: ${provisioningQueue.length}`);

  if (isProvisioning || provisioningQueue.length === 0) {
    console.log(`[${timestamp}] [Queue Processor] Exiting early - isProvisioning: ${isProvisioning}, queue empty: ${provisioningQueue.length === 0}`);
    return;
  }

  isProvisioning = true;
  const task = provisioningQueue.shift();
  console.log(`[${timestamp}] [Queue Processor] Dequeued task. Params:`, JSON.stringify(task?.params, null, 2));

  const { instanceId = 'ERR_ID', port = -1, userId = 'ERR_USR', apiUsername = 'ERR_API', apiPassword = 'ERR_PW' } = task?.params || {};
  console.log(`[${timestamp}] [${instanceId}] PROVISIONING START - userId: ${userId}, port: ${port}`);

  // Define paths: ensure user directory, then instance directory
  const userDir = path.join(BOT_BASE_DIR, userId);
  await fs.ensureDir(userDir);
  const instanceDir = path.join(userDir, instanceId);
  const userDataDir = path.join(instanceDir, 'user_data');
  const instanceStrategiesDir = path.join(userDataDir, 'strategies');
  const instanceDataDir = path.join(userDataDir, 'data');

  console.log(`[${instanceId}] STEP 0: Directory paths defined:
    userDir: ${userDir}
    instanceDir: ${instanceDir}
    userDataDir: ${userDataDir}
    instanceStrategiesDir: ${instanceStrategiesDir}
    instanceDataDir: ${instanceDataDir}`);

  try {
    console.log(`[${instanceId}] STEP 1: Creating instance directories...`);
    // --- 1. Create Instance Dirs ---
    await fs.ensureDir(userDir);
    console.log(`[${instanceId}] ✓ User directory created: ${userDir}`);

    await fs.ensureDir(instanceDir);
    console.log(`[${instanceId}] ✓ Instance directory created: ${instanceDir}`);

    await fs.ensureDir(userDataDir);
    console.log(`[${instanceId}] ✓ User data directory created: ${userDataDir}`);

    await fs.ensureDir(instanceStrategiesDir);
    console.log(`[${instanceId}] ✓ Strategies directory created: ${instanceStrategiesDir}`);

    await fs.ensureDir(path.join(userDataDir, 'logs'));
    console.log(`[${instanceId}] ✓ Logs directory created: ${path.join(userDataDir, 'logs')}`);

    await fs.ensureDir(instanceDataDir); // Create structure like user_data/data/
    console.log(`[${instanceId}] ✓ Data directory created: ${instanceDataDir}`);

    console.log(`[${instanceId}] STEP 1 COMPLETE: Instance directories created.`);

    console.log(`[${instanceId}] STEP 2: Setting permissions on user data directory...`);
    // --- 2. Set Permissions ---
    try {
      const chmod = spawn('chmod', ['-R', '777', userDataDir]);
      chmod.stderr.on('data', (data) => { console.error(`[${instanceId}] chmod stderr: ${data}`); });
      await new Promise((resolve, reject) => {
        chmod.on('error', (err) => {
          console.error(`[${instanceId}] Failed spawn chmod:`, err);
          reject(err);
        });
        chmod.on('close', (code) => {
          if (code === 0) {
            console.log(`[${instanceId}] ✓ Permissions set successfully on ${userDataDir}`);
            resolve();
          } else {
            console.error(`[${instanceId}] chmod exited with code ${code}`);
            reject(new Error(`chmod exited code ${code}`));
          }
        });
      });
      console.log(`[${instanceId}] STEP 2 COMPLETE: Permissions set.`);
    } catch (chmodError) {
      console.error(`[${instanceId}] STEP 2 FAILED: Permissions error:`, chmodError);
      throw new Error(`Permissions failed: ${chmodError.message}`);
    }

    console.log(`[${instanceId}] STEP 3: Copying strategies from ${MAIN_STRATEGIES_SOURCE_DIR} to ${instanceStrategiesDir}...`);
    // --- 3. Copy Strategies ---
    try {
      if (await fs.pathExists(MAIN_STRATEGIES_SOURCE_DIR)) {
        console.log(`[${instanceId}] ✓ Source strategies directory exists, starting copy...`);
        await fs.copy(MAIN_STRATEGIES_SOURCE_DIR, instanceStrategiesDir, {
          overwrite: true, filter: (src) => {
            const stats = fs.statSync(src);
            return stats.isDirectory() || path.extname(src) === '.py';
          }
        });
        console.log(`[${instanceId}] ✓ Strategies copied successfully.`);

        const copiedFiles = await fs.readdir(instanceStrategiesDir);
        const pyFiles = copiedFiles.filter(f => f.endsWith('.py'));
        console.log(`[${instanceId}] Copied strategy files: ${pyFiles.join(', ') || 'none'}`);

        if (pyFiles.length === 0) {
          console.warn(`[${instanceId}] WARNING: Source dir had no .py files, using fallback.`);
          const defaultStrategySource = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
          if (await fs.pathExists(defaultStrategySource)) {
            await fs.copy(defaultStrategySource, path.join(instanceStrategiesDir, 'DefaultStrategy.py'));
            console.log(`[${instanceId}] ✓ Copied fallback DefaultStrategy.`);
          }
        }
      } else {
        console.warn(`[${instanceId}] WARNING: Main source dir ${MAIN_STRATEGIES_SOURCE_DIR} not found, using fallback.`);
        const defaultStrategySource = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
        if (await fs.pathExists(defaultStrategySource)) {
          await fs.copy(defaultStrategySource, path.join(instanceStrategiesDir, 'DefaultStrategy.py'));
          console.log(`[${instanceId}] ✓ Copied fallback DefaultStrategy.`);
        } else {
          console.error(`[${instanceId}] ERROR: No strategies found anywhere!`);
        }
      }
      console.log(`[${instanceId}] STEP 3 COMPLETE: Strategy copying finished.`);
    } catch (copyError) {
      console.error(`[${instanceId}] STEP 3 ERROR: Strategy copy failed:`, copyError);
      console.warn(`[${instanceId}] Continuing despite copy error.`);
    }

    console.log(`[${instanceId}] STEP 4: Creating base configuration...`);
    // --- 4. Create config.json ---
    let defaultStrategyToUse = "DefaultStrategy";
    const instanceStrategyFiles = await fs.readdir(instanceStrategiesDir);
    const pyFiles = instanceStrategyFiles.filter(f => f.endsWith('.py'));
    console.log(`[${instanceId}] Available strategy files: ${pyFiles.join(', ') || 'none'}`);

    if (pyFiles.length > 0) {
      if (pyFiles.includes('EmaRsiStrategy.py')) {
        defaultStrategyToUse = 'EmaRsiStrategy';
        console.log(`[${instanceId}] Selected EmaRsiStrategy`);
      } else if (pyFiles.includes('DefaultStrategy.py')) {
        defaultStrategyToUse = 'DefaultStrategy';
        console.log(`[${instanceId}] Selected DefaultStrategy`);
      } else {
        defaultStrategyToUse = pyFiles[0].replace('.py', '');
        console.log(`[${instanceId}] Selected first available strategy: ${defaultStrategyToUse}`);
      }
    } else {
      console.warn(`[${instanceId}] No strategies in instance dir. Defaulting to ${defaultStrategyToUse}.`);
    }

    // Enforce Turso remote DB only; default empty and must be set after Turso provisioning
    const configJson = {
      userId: userId,
      max_open_trades: 25, stake_currency: "USD", stake_amount: 100, tradable_balance_ratio: 1,
      // Dry-run flag preserved; remote Turso DB always provisioned
      dry_run: true, cancel_open_orders_on_exit: false,
      trading_mode: "spot", margin_mode: "isolated", strategy: defaultStrategyToUse,
      // DB URL set after Turso provisioning
      db_url: "", logfile: "/freqtrade/user_data/logs/freqtrade.log",
      timeframe: '15m', // Ensure timeframe is defined
      unfilledtimeout: { entry: 10, exit: 10, exit_timeout_count: 0, unit: "minutes" },
      entry_pricing: { price_side: "same", use_order_book: true, order_book_top: 1, price_last_balance: 0, check_depth_of_market: { enabled: false, bids_to_ask_delta: 1 } },
      exit_pricing: { price_side: "same", use_order_book: true, order_book_top: 1 },
      exchange: { name: "kraken", key: "", secret: "", ccxt_config: {}, ccxt_async_config: {}, pair_whitelist: ["BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD", "DOT/USD"], pair_blacklist: [] },
      pairlists: [{ method: "StaticPairList" }],
      telegram: { enabled: false, token: "", chat_id: "" },
      api_server: { enabled: true, listen_ip_address: "0.0.0.0", listen_port: port, verbosity: "info", enable_openapi: true, jwt_secret_key: `aVeryStr0ngStaticPrefix!_${instanceId}_KeepSecret`, CORS_origins: ["*"], username: apiUsername, password: apiPassword },
      bot_name: `${userId}-bot`, initial_state: "running", force_entry_enable: false,
      internals: { process_throttle_secs: 30 }
    };
    console.log(`[${instanceId}] Generated config with strategy: ${configJson.strategy}, port: ${port}`);

    // Write base config.json immediately
    const configPath = path.join(instanceDir, 'config.json');
    await fs.writeFile(configPath, JSON.stringify(configJson, null, 2));
    console.log(`[${instanceId}] ✓ Base config.json written to: ${configPath}`);
    console.log(`[${instanceId}] STEP 4 COMPLETE: Base configuration created.`);

    console.log(`[${instanceId}] STEP 5: Provisioning Turso database...`);
    // --- TURSO: Always provision remote DB after base config is written ---
    if (TURSO_API_KEY) {
      console.log(`[${instanceId}] ✓ TURSO_API_KEY available, proceeding with database provisioning...`);
      try {
        const tursoNameOrig = `bot-${userId}-${instanceId}`;
        // Sanitize name, create and fetch metadata
        let tursoName = tursoNameOrig.toLowerCase()
          .replace(/[^a-z0-9-]/g, '-')    // replace invalid chars with dashes
          .replace(/-+/g, '-')           // collapse consecutive dashes
          .replace(/(^-|-$)/g, '');      // trim leading/trailing dashes
        console.log(`[${instanceId}] Sanitized Turso DB name: '${tursoName}' (from '${tursoNameOrig}')`);

        console.log(`[${instanceId}] Creating Turso DB '${tursoName}' in region '${TURSO_REGION}'...`);
        // Step 1: Create the DB (CLI does not support JSON output on create)
        await new Promise((resolve, reject) => {
          const createProc = spawn(TURSO_CMD, ['db', 'create', tursoName, '--location', TURSO_REGION, '--wait'], { cwd: instanceDir });
          createProc.on('error', err => {
            console.error(`[${instanceId}] Turso spawn error:`, err);
            reject(err);
          });
          createProc.stdout.on('data', data => console.log(`[${instanceId}] turso create stdout: ${data}`));
          createProc.stderr.on('data', d => console.error(`[${instanceId}] turso create stderr: ${d}`));
          createProc.on('close', code => {
            if (code !== 0) {
              console.error(`[${instanceId}] Turso create failed (code: ${code}).`);
              return reject(new Error(`turso create exited ${code}`)); // Ensure rejection on failure
            }
            console.log(`[${instanceId}] ✓ Turso DB '${tursoName}' created successfully.`);
            resolve();
          });
        });

        console.log(`[${instanceId}] Fetching Turso DB URL...`);
        // Step 2: Fetch Turso DB URL (HTTP API endpoint)
        let rawUrl = '';
        try {
          console.log(`[${instanceId}] Trying CLI method to get DB URL...`);
          // Try CLI first
          let urlOut = '';
          await new Promise((resolve, reject) => {
            const showProc = spawn(TURSO_CMD, ['db', 'show', tursoName, '--url'], { cwd: instanceDir });
            showProc.on('error', err => reject(err));
            showProc.stdout.on('data', data => {
              urlOut += data;
              console.log(`[${instanceId}] turso show stdout: ${data}`);
            });
            showProc.stderr.on('data', data => console.error(`[${instanceId}] turso show stderr: ${data}`));
            showProc.on('close', code => code === 0 ? resolve() : reject(new Error(`turso show exited ${code}`)));
          });
          rawUrl = urlOut.trim();
          if (!rawUrl) throw new Error('Empty URL from CLI');
          console.log(`[${instanceId}] ✓ Retrieved rawUrl via CLI: ${rawUrl}`);
        } catch (cliErr) {
          console.warn(`[${instanceId}] CLI method failed: ${cliErr.message}, trying HTTP API...`);
          // HTTP API fallback
          console.log(`[${instanceId}] Making HTTP API request to Turso...`);
          const apiUrl = `https://api.turso.co/v1/dbs/${tursoName}?include=instances`;
          console.log(`[${instanceId}] API URL: ${apiUrl}`);
          const resp = await fetch(apiUrl, { headers: { Authorization: `Bearer ${TURSO_API_KEY}` } });
          if (!resp.ok) {
            console.error(`[${instanceId}] HTTP API response not ok: ${resp.status} ${resp.statusText}`);
            throw new Error(`Turso HTTP API error: ${resp.status}`);
          }
          const info = await resp.json();
          console.log(`[${instanceId}] HTTP API response:`, JSON.stringify(info, null, 2));
          const inst = info.data?.instances?.[0];
          if (!inst) {
            console.error(`[${instanceId}] No instances found in HTTP response`);
            throw new Error('No instances in HTTP response');
          }
          rawUrl = inst.libsqlUrl || inst.url;
          if (!rawUrl) {
            console.error(`[${instanceId}] No libsql URL found in instance data:`, inst);
            throw new Error('No libsql URL in HTTP response');
          }
          console.log(`[${instanceId}] ✓ Retrieved rawUrl via HTTP API: ${rawUrl}`);
        }

        console.log(`[${instanceId}] Formatting database URL for SQLAlchemy...`);
        // Ensure rawUrl is set and normalized for SQLAlchemy logging
        try {
          configJson.db_url = formatDbUrl(rawUrl, tursoName);
          console.log(`[${instanceId}] ✓ Formatted URL via formatDbUrl function: ${configJson.db_url}`);
        } catch (urlErr) {
          console.warn(`[${instanceId}] formatDbUrl error: ${urlErr.message}, using manual fallback`);
          // Better manual fallback for Turso URLs
          if (rawUrl.includes('turso.io')) {
            // Extract hostname from the raw URL
            const urlMatch = rawUrl.match(/https?:\/\/([^\/]+)/);
            if (urlMatch) {
              const host = urlMatch[1];
              configJson.db_url = `sqlite+libsql://${host}?authToken=${TURSO_API_KEY}`;
              console.log(`[${instanceId}] ✓ Manual fallback URL created: ${configJson.db_url}`);
            } else {
              configJson.db_url = rawUrl;
              console.log(`[${instanceId}] Using raw URL as fallback: ${configJson.db_url}`);
            }
          } else {
            configJson.db_url = rawUrl;
            console.log(`[${instanceId}] Using raw URL (not turso.io): ${configJson.db_url}`);
          }
        }

        console.log(`[${instanceId}] Updating config.json with database URL...`);
        await fs.writeFile(configPath, JSON.stringify(configJson, null, 2));
        console.log(`[${instanceId}] ✓ config.json updated with Turso db_url: ${configJson.db_url}`);
        console.log(`[${instanceId}] STEP 5 COMPLETE: Turso database provisioned successfully.`);
      } catch (tursoErr) {
        console.error(`[${instanceId}] STEP 5 FAILED: Turso provisioning failed, aborting provisioning.`, tursoErr);
        throw tursoErr;
      }
    } else {
      console.error(`[${instanceId}] STEP 5 FAILED: TURSO_API_KEY must be provided.`);
      throw new Error('Missing Turso configuration');
    }
    console.log(`[${instanceId}] Final DB URL after Turso provisioning: ${configJson.db_url}`);

    console.log(`[${instanceId}] STEP 6: Preparing Docker container...`);
    // --- 6. Start Container via docker compose ---
    const exchangeName = configJson.exchange.name;
    if (!exchangeName) {
      console.error(`[${instanceId}] Exchange name missing in configJson, cannot mount shared data.`);
      throw new Error("Exchange name missing in configJson, cannot mount shared data.");
    }
    console.log(`[${instanceId}] Exchange name: ${exchangeName}`);

    const hostSharedExchangeDataPath = path.join(SHARED_DATA_DIR, exchangeName);
    console.log(`[${instanceId}] Checking shared data path: ${hostSharedExchangeDataPath}`);
    if (!await fs.pathExists(hostSharedExchangeDataPath)) {
      console.warn(`[${instanceId}] WARNING: Shared data directory for exchange '${exchangeName}' (${hostSharedExchangeDataPath}) does not exist on host. Bot may fail if data is required.`);
    } else {
      console.log(`[${instanceId}] ✓ Shared data directory exists: ${hostSharedExchangeDataPath}`);
    }

    const containerName = `freqtrade-${instanceId}`;
    console.log(`[${instanceId}] Container name: ${containerName}`);

    // Remove any existing container with the same name to prevent conflicts
    console.log(`[${instanceId}] Removing any existing container '${containerName}' (if exists)...`);
    try {
      await runDockerCommand(['rm', '-f', containerName]);
      console.log(`[${instanceId}] ✓ Removed existing container '${containerName}'.`);
    } catch (rmErr) {
      console.log(`[${instanceId}] No existing container to remove: ${rmErr.message}`);
    }
    console.log(`[${instanceId}] Creating entrypoint script and docker-compose configuration...`);
    // Use absolute paths for mounts
    const userDataHostPath = path.join(instanceDir, 'user_data');
    const configHostPath = path.join(instanceDir, 'config.json');
    const entrypointHostPath = path.join(instanceDir, 'entrypoint.sh');

    console.log(`[${instanceId}] Mount paths:
      userDataHostPath: ${userDataHostPath}
      configHostPath: ${configHostPath}
      entrypointHostPath: ${entrypointHostPath}
      hostSharedExchangeDataPath: ${hostSharedExchangeDataPath}`);

    const entrypointScript = `#!/bin/bash
set -e

# Set database URL environment variable
export DB_URL="${configJson.db_url}"

echo "[ENTRYPOINT] Starting FreqTrade container for instance ${instanceId}"
echo "[ENTRYPOINT] Database URL: \$DB_URL"

# Create needed directories and initialize environment
echo "[ENTRYPOINT] Ensuring FreqTrade user directory exists..."
freqtrade create-userdir --userdir /freqtrade/user_data

# CRITICAL: Initialize database schema with GUARANTEED table creation
echo "[ENTRYPOINT] CRITICAL: Ensuring database tables exist before FreqTrade starts..."
python3 - << 'PYCODE'
import os
import time
import sqlite3
from sqlalchemy.exc import OperationalError as SAOperationalError

def ensure_database_tables(db_url, max_attempts=5):
    """Ensure database tables exist with aggressive retry logic"""
    print(f"[ENTRYPOINT] CRITICAL: Ensuring database tables exist for: {db_url}")

    for attempt in range(max_attempts):
        try:
            print(f"[ENTRYPOINT] CRITICAL: Database table creation attempt {attempt + 1}/{max_attempts}")

            # Import and initialize database
            from freqtrade.persistence.models import init_db
            init_db(db_url)
            print(f"[ENTRYPOINT] CRITICAL: ✓ init_db() completed on attempt {attempt + 1}")

            # Verify tables exist by trying to query them
            from freqtrade.persistence import Trade
            from freqtrade.persistence.key_value_store import _KeyValueStoreModel

            # Test Trade table
            try:
                Trade.session.query(Trade).first()
                print("[ENTRYPOINT] CRITICAL: ✓ Trade table verified")
            except Exception as e:
                print(f"[ENTRYPOINT] CRITICAL: Trade table verification failed: {e}")
                raise

            # Test KeyValueStore table
            try:
                _KeyValueStoreModel.session.query(_KeyValueStoreModel).first()
                print("[ENTRYPOINT] CRITICAL: ✓ KeyValueStore table verified")
            except Exception as e:
                print(f"[ENTRYPOINT] CRITICAL: KeyValueStore table verification failed: {e}")
                raise

            print(f"[ENTRYPOINT] CRITICAL: ✓ ALL TABLES VERIFIED on attempt {attempt + 1}")
            return True

        except (sqlite3.OperationalError, SAOperationalError) as e:
            print(f"[ENTRYPOINT] CRITICAL: Database attempt {attempt + 1} failed: {e}")
            if attempt < max_attempts - 1:
                wait_time = (attempt + 1) * 2  # Increasing wait time
                print(f"[ENTRYPOINT] CRITICAL: Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                print(f"[ENTRYPOINT] CRITICAL: ALL ATTEMPTS FAILED - FreqTrade may crash!")
                return False
        except Exception as e:
            print(f"[ENTRYPOINT] CRITICAL: Unexpected error: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2)
            else:
                return False

# Ensure database tables exist with aggressive retry
success = ensure_database_tables(os.environ['DB_URL'])
if success:
    print("[ENTRYPOINT] CRITICAL: ✓ DATABASE READY - FreqTrade should start successfully")
else:
    print("[ENTRYPOINT] CRITICAL: ⚠ DATABASE NOT READY - FreqTrade may encounter errors")
PYCODE

echo "[ENTRYPOINT] Starting FreqTrade trading..."
exec freqtrade trade --config /freqtrade/config.json --db-url "\$DB_URL"`;

    await fs.writeFile(entrypointHostPath, entrypointScript, { mode: 0o755 });
    console.log(`[${instanceId}] ✓ Entrypoint script written to: ${entrypointHostPath}`);

    // --- Write docker-compose.yml for this instance ---
    const composePath = path.join(instanceDir, 'docker-compose.yml');
    const composeContent = `version: '3.8'
services:
  freqtrade:
    image: ${FREQTRADE_IMAGE}
    container_name: freqtrade-${instanceId}
    restart: unless-stopped
    network_mode: host
    entrypoint: ["/freqtrade/entrypoint.sh"]
    environment:
      - DB_URL=${configJson.db_url}
      - LOGFILE=${configJson.logfile}
    volumes:
      - ${entrypointHostPath}:/freqtrade/entrypoint.sh:ro
      - ${userDataHostPath}:/freqtrade/user_data
      - ${configHostPath}:/freqtrade/config.json:ro
      - ${hostSharedExchangeDataPath}:/freqtrade/user_data/data/${exchangeName}:ro
`;
    await fs.writeFile(composePath, composeContent);
    console.log(`[${instanceId}] ✓ docker-compose.yml written to: ${composePath}`);

    console.log(`[${instanceId}] STEP 7: Starting Docker container...`);

    // Try starting with docker compose, fallback to direct docker run on failure
    try {
      console.log(`[${instanceId}] Attempting to start container via docker-compose...`);
      // Use 'docker-compose' binary directly for compatibility
      await runDockerCommand([
        '-f', composePath,
        'up', '-d'
      ], instanceDir, 'docker-compose');
      console.log(`[${instanceId}] ✓ Container started successfully via 'docker-compose'.`);
    } catch (composeErr) {
      console.warn(`[${instanceId}] docker-compose up failed: ${composeErr.message}. Falling back to direct docker run.`);
      console.log(`[${instanceId}] Attempting fallback: direct docker run...`);

      try {
        await runDockerCommand([
          'run', '-d', '--name', containerName, '--network', 'host',
          '-v', `${entrypointHostPath}:/freqtrade/entrypoint.sh:ro`,
          '-v', `${userDataHostPath}:/freqtrade/user_data`,
          '-v', `${configHostPath}:/freqtrade/config.json:ro`,
          '-v', `${hostSharedExchangeDataPath}:/freqtrade/user_data/data/${exchangeName}:ro`,
          '-e', `DB_URL=${configJson.db_url}`,
          '--entrypoint', '/freqtrade/entrypoint.sh',
          FREQTRADE_IMAGE
        ], instanceDir);
        console.log(`[${instanceId}] ✓ Container started successfully via direct docker run fallback.`);
      } catch (dockerRunErr) {
        console.error(`[${instanceId}] STEP 7 FAILED: Both docker-compose and docker run failed.`);
        console.error(`[${instanceId}] docker-compose error:`, composeErr);
        console.error(`[${instanceId}] docker run error:`, dockerRunErr);
        throw dockerRunErr;
      }
    }

    console.log(`[${instanceId}] STEP 7 COMPLETE: Docker container started.`);

    // Verify container is running
    console.log(`[${instanceId}] STEP 8: Verifying container status...`);
    try {
      const containerStatus = await runDockerCommand(['ps', '-f', `name=${containerName}`, '--format', 'table {{.Names}}\\t{{.Status}}']);
      console.log(`[${instanceId}] Container status: ${containerStatus}`);
      if (containerStatus.includes(containerName)) {
        console.log(`[${instanceId}] ✓ Container is running.`);
      } else {
        console.warn(`[${instanceId}] Container may not be running properly.`);
      }
    } catch (statusErr) {
      console.warn(`[${instanceId}] Could not verify container status:`, statusErr.message);
    }

    console.log(`[${instanceId}] STEP 8 COMPLETE: Container verification finished.`);

    // --- Provisioning Complete ---
    console.log(`[${instanceId}] ==========================================`);
    console.log(`[${instanceId}] PROVISIONING COMPLETE - SUCCESS!`);
    console.log(`[${instanceId}] Summary:`);
    console.log(`[${instanceId}]   User ID: ${userId}`);
    console.log(`[${instanceId}]   Instance ID: ${instanceId}`);
    console.log(`[${instanceId}]   Port: ${port}`);
    console.log(`[${instanceId}]   Container: ${containerName}`);
    console.log(`[${instanceId}]   Strategy: ${configJson.strategy}`);
    console.log(`[${instanceId}]   Database URL: ${configJson.db_url}`);
    console.log(`[${instanceId}]   Instance Directory: ${instanceDir}`);
    console.log(`[${instanceId}] ==========================================`);

    if (task.res && !task.res.headersSent) {
      const response = {
        success: true,
        message: 'Bot provisioned successfully',
        instanceId: instanceId,
        port: port,
        containerName: containerName,
        strategy: configJson.strategy
      };
      task.res.json(response);
      console.log(`[${instanceId}] ✓ Sent final success response:`, JSON.stringify(response, null, 2));
    } else {
      console.warn(`[${instanceId}] Headers already sent, cannot send response.`);
    }

  } catch (error) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] [${instanceId}] ==========================================`);
    console.error(`[${timestamp}] [${instanceId}] PROVISIONING FAILED - CRITICAL ERROR`);
    console.error(`[${timestamp}] [${instanceId}] ==========================================`);
    console.error(`[${timestamp}] [${instanceId}] Error Message:`, error.message);
    console.error(`[${timestamp}] [${instanceId}] Error Stack:`, error.stack);
    console.error(`[${timestamp}] [${instanceId}] Full Error Object:`, JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

    // Try to gather additional debugging info
    try {
      console.error(`[${timestamp}] [${instanceId}] Additional Debug Info:`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_API_KEY available: ${!!TURSO_API_KEY}`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_CMD: ${TURSO_CMD}`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_REGION: ${TURSO_REGION}`);
      console.error(`[${timestamp}] [${instanceId}]   FREQTRADE_IMAGE: ${FREQTRADE_IMAGE}`);
      console.error(`[${timestamp}] [${instanceId}]   BOT_BASE_DIR: ${BOT_BASE_DIR}`);
      console.error(`[${timestamp}] [${instanceId}]   Instance dir exists: ${await fs.pathExists(instanceDir)}`);
      if (await fs.pathExists(instanceDir)) {
        const files = await fs.readdir(instanceDir);
        console.error(`[${timestamp}] [${instanceId}]   Instance dir contents: ${files.join(', ')}`);
      }
    } catch (debugErr) {
      console.error(`[${timestamp}] [${instanceId}] Could not gather debug info:`, debugErr.message);
    }

    console.error(`[${timestamp}] [${instanceId}] ==========================================`);

    if (task.res && !task.res.headersSent) {
      const errorResponse = {
        success: false,
        message: `Server error for ${instanceId}`,
        error: error.message || 'Unknown error',
        instanceId: instanceId,
        timestamp: timestamp
      };
      task.res.status(500).json(errorResponse);
      console.log(`[${timestamp}] [${instanceId}] Sent error response:`, JSON.stringify(errorResponse, null, 2));
    } else {
      console.error(`[${timestamp}] [${instanceId}] Cannot send error response (headers already sent).`);
    }
  } finally {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${instanceId}] Entering FINALLY block - cleaning up...`);
    isProvisioning = false;
    console.log(`[${timestamp}] [${instanceId}] Set isProvisioning = false`);

    setTimeout(() => {
      console.log(`[${timestamp}] [${instanceId}] Scheduling next queue processing in 500ms...`);
      processProvisioningQueue();
    }, 500);

    console.log(`[${timestamp}] [${instanceId}] PROVISIONING PROCESS FINISHED.`);
    console.log(`[${timestamp}] [${instanceId}] Queue length now: ${provisioningQueue.length}`);
  }
}


// --- Start processing the queue periodically (Fallback) ---
setInterval(() => {
  if (!isProvisioning && provisioningQueue.length > 0) {
    console.log("[Queue Interval] Interval check found tasks. Starting processing.");
    processProvisioningQueue();
  }
}, 5000);

// --- Helper Function to Run Docker Commands ---
async function runDockerCommand(args, cwd = null, cmdName = 'docker') {
  return new Promise((resolve, reject) => {
    const cmd = cmdName;
    const cmdArgs = args;
    const options = cwd ? { cwd } : {};
    options.encoding = 'utf8';

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [Docker Helper] Starting command: ${cmd} ${cmdArgs.join(' ')} ${cwd ? `(in ${cwd})` : ''}`);

    const command = spawn(cmd, cmdArgs, options);
    let stdout = '';
    let stderr = '';

    command.stdout.on('data', (data) => {
      stdout += data;
      console.log(`[${timestamp}] [Docker Helper] stdout: ${data.toString().trim()}`);
    });

    command.stderr.on('data', (data) => {
      stderr += data;
      console.error(`[${timestamp}] [Docker Helper] stderr: ${data.toString().trim()}`);
    });

    command.on('error', (err) => {
      console.error(`[${timestamp}] [Docker Helper] Spawn Error: ${cmd} ${cmdArgs.join(' ')}`, err);
      reject(err);
    });

    command.on('close', (code) => {
      console.log(`[${timestamp}] [Docker Helper] Command finished: ${cmd} ${cmdArgs.join(' ')} (Exit Code: ${code})`);

      if (stderr && code !== 0) {
        console.error(`[${timestamp}] [Docker Helper] Final stderr: ${stderr.trim()}`);
      }

      if (stdout.trim()) {
        console.log(`[${timestamp}] [Docker Helper] Final stdout: ${stdout.trim()}`);
      }

      if (code !== 0) {
        const errorMsg = `Docker command failed with exit code ${code}: ${stderr.trim() || stdout.trim() || 'No output'}`;
        console.error(`[${timestamp}] [Docker Helper] ${errorMsg}`);
        reject(new Error(errorMsg));
      } else {
        console.log(`[${timestamp}] [Docker Helper] Command succeeded.`);
        resolve(stdout.trim());
      }
    });
  });
}

// Helper to resolve instance directory across user subdirectories
async function resolveInstanceDir(instanceId) {
  // Check flat structure first
  const direct = path.join(BOT_BASE_DIR, instanceId);
  if (await fs.pathExists(direct)) return direct;
  // Otherwise scan each user's folder
  const users = await fs.readdir(BOT_BASE_DIR);
  for (const uid of users) {
    const candidate = path.join(BOT_BASE_DIR, uid, instanceId);
    if (await fs.pathExists(candidate)) return candidate;
  }
  throw new Error(`Instance ${instanceId} not found`);
}

// --- FreqTrade API Proxy Routes ---
// Helper function to get bot URL by instanceId
async function getBotUrlByInstanceId(instanceId) {
  try {
    const instanceDir = await resolveInstanceDir(instanceId);
    const configPath = path.join(instanceDir, 'config.json');

    if (!await fs.pathExists(configPath)) {
      throw new Error(`Instance ${instanceId} not found or has no config file`);
    }

    const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
    const port = config.api_server?.listen_port;

    if (!port) {
      throw new Error(`No API port configured for instance ${instanceId}`);
    }

    // Use IPv4 loopback (127.0.0.1) to avoid IPv6 connect ECONNREFUSED
    return {
      url: `http://127.0.0.1:${port}`,
      username: config.api_server?.username,
      password: config.api_server?.password
    };
  } catch (error) {
    console.error(`Error getting bot URL for ${instanceId}:`, error);
    throw error;
  }
}

// Helper function to get auth token for a bot
async function getBotAuthToken(botConfig) {
  try {
    // Use HTTP Basic auth for login endpoint
    const basicAuth = Buffer.from(`${botConfig.username}:${botConfig.password}`).toString('base64');
    const response = await fetch(`${botConfig.url}/api/v1/token/login`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${basicAuth}`
      }
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get bot token: ${error}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error('Error getting bot auth token:', error);
    throw error;
  }
}

// Generic proxy handler for any FreqTrade API endpoint
async function proxyFreqtradeApiRequest(instanceId, endpoint, method = 'GET', body = null) {
  try {
    const botConfig = await getBotUrlByInstanceId(instanceId);
    const token = await getBotAuthToken(botConfig);

    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(`${botConfig.url}${endpoint}`, options);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`FreqTrade API error (${response.status}): ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error proxying API request to ${instanceId}${endpoint}:`, error);
    throw error;
  }
}

// Get all trades for a specific bot
app.get('/api/bots/:instanceId/trades', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const trades = await proxyFreqtradeApiRequest(instanceId, '/api/v1/trades');
    res.json(trades);
  } catch (error) {
    console.error(`Error fetching trades for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch trades: ${error.message}`
    });
  }
});

// Get open trades for a specific bot
app.get('/api/bots/:instanceId/status', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const status = await proxyFreqtradeApiRequest(instanceId, '/api/v1/status');
    res.json(status);
  } catch (error) {
    console.error(`Error fetching status for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch status: ${error.message}`
    });
  }
});

// Get profit information for a specific bot
app.get('/api/bots/:instanceId/profit', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const profit = await proxyFreqtradeApiRequest(instanceId, '/api/v1/profit');
    res.json(profit);
  } catch (error) {
    console.error(`Error fetching profit for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch profit: ${error.message}`
    });
  }
});

// Get performance by pair for a specific bot
app.get('/api/bots/:instanceId/performance', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const performance = await proxyFreqtradeApiRequest(instanceId, '/api/v1/performance');
    res.json(performance);
  } catch (error) {
    console.error(`Error fetching performance for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch performance: ${error.message}`
    });
  }
});

// Get balance information for a specific bot
app.get('/api/bots/:instanceId/balance', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    console.log(`[Balance API] Processing balance request for bot: ${instanceId}`);
    console.log(`[Balance API] User requesting: ${req.user.id || req.user.uid}`);

    try {
      const botConfig = await getBotUrlByInstanceId(instanceId);
      console.log(`[Balance API] Bot config retrieved successfully: ${JSON.stringify(botConfig.url)}`);

      try {
        const token = await getBotAuthToken(botConfig);
        console.log(`[Balance API] Bot token retrieved successfully`);

        const options = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        };

        console.log(`[Balance API] Making request to: ${botConfig.url}/api/v1/balance`);
        const response = await fetch(`${botConfig.url}/api/v1/balance`, options);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`[Balance API] FreqTrade API error (${response.status}): ${errorText}`);
          throw new Error(`FreqTrade API error (${response.status}): ${errorText}`);
        }

        const balance = await response.json();
        console.log(`[Balance API] Balance retrieved successfully`);
        res.json(balance);
      } catch (tokenError) {
        console.error(`[Balance API] Error getting bot token: ${tokenError.message}`, tokenError);
        res.status(500).json({
          success: false,
          message: `Failed to get bot token: ${tokenError.message}`
        });
      }
    } catch (configError) {
      console.error(`[Balance API] Error getting bot config: ${configError.message}`, configError);
      res.status(500).json({
        success: false,
        message: `Failed to get bot config: ${configError.message}`
      });
    }
  } catch (error) {
    console.error(`[Balance API] Error fetching balance for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch balance: ${error.message}`
    });
  }
});

// Get whitelist for a specific bot
app.get('/api/bots/:instanceId/whitelist', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const whitelist = await proxyFreqtradeApiRequest(instanceId, '/api/v1/whitelist');
    res.json(whitelist);
  } catch (error) {
    console.error(`Error fetching whitelist for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch whitelist: ${error.message}`
    });
  }
});

// Get daily stats for a specific bot
app.get('/api/bots/:instanceId/daily', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const daily = await proxyFreqtradeApiRequest(instanceId, '/api/v1/daily');
    res.json(daily);
  } catch (error) {
    console.error(`Error fetching daily stats for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch daily stats: ${error.message}`
    });
  }
});

// Handle trade actions (buy, sell, etc.) for a specific bot
app.post('/api/bots/:instanceId/trades/:action', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId, action } = req.params;
    const endpoint = `/api/v1/${action}`;
    const result = await proxyFreqtradeApiRequest(instanceId, endpoint, 'POST', req.body);
    res.json(result);
  } catch (error) {
    console.error(`Error performing trade action ${req.params.action} for ${req.params.instanceId}:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to perform trade action: ${error.message}`
    });
  }
});

// Aggregate data from all bots for a user
app.get('/api/user/dashboard', authenticateToken, async (req, res) => {
  try {
    // Get user ID from the authenticated token
    const userId = req.user.id || req.user.uid;

    // Find all bot instances owned by this user
    const instanceDirs = await fs.readdir(BOT_BASE_DIR);
    const userBots = [];

    for (const instanceId of instanceDirs) {
      try {
        const instanceDir = path.join(BOT_BASE_DIR, instanceId);
        const stats = await fs.stat(instanceDir);

        if (!stats.isDirectory()) continue;

        const configPath = path.join(instanceDir, 'config.json');
        if (!await fs.pathExists(configPath)) continue;

        const config = JSON.parse(await fs.readFile(configPath, 'utf8'));

        // Check if this bot belongs to the user
        if (config.bot_name && config.bot_name.startsWith(`${userId}-`)) {
          userBots.push(instanceId);
        }
      } catch (err) {
        console.error(`Error checking bot instance ${instanceId}:`, err);
      }
    }

    // Collect data from all user's bots
    const dashboard = {
      totalProfit: 0,
      openTrades: [],
      closedTrades: [],
      botStatuses: []
    };

    for (const instanceId of userBots) {
      try {
        // Get bot status
        const status = {
          instanceId,
          status: 'unknown',
          error: null
        };

        try {
          const botStatus = await proxyFreqtradeApiRequest(instanceId, '/api/v1/status');
          const profit = await proxyFreqtradeApiRequest(instanceId, '/api/v1/profit');

          // Add bot data to dashboard
          dashboard.totalProfit += profit.profit_all_coin || 0;
          dashboard.openTrades = dashboard.openTrades.concat(
            botStatus.map(trade => ({ ...trade, bot: instanceId }))
          );

          status.status = 'running';
          status.runningTradeCount = botStatus.length;
          status.botProfit = profit.profit_all_coin || 0;
        } catch (err) {
          status.status = 'error';
          status.error = err.message;
        }

        dashboard.botStatuses.push(status);
      } catch (err) {
        console.error(`Error fetching data for bot ${instanceId}:`, err);
      }
    }

    res.json({
      success: true,
      bots: userBots.length,
      dashboard
    });
  } catch (error) {
    console.error(`Error fetching user dashboard:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch dashboard data: ${error.message}`
    });
  }
});

// Get all trades across all user's bots
app.get('/api/user/trades', authenticateToken, async (req, res) => {
  try {
    // Get user ID from the authenticated token
    const userId = req.user.id || req.user.uid;

    // Find all bot instances owned by this user
    const instanceDirs = await fs.readdir(BOT_BASE_DIR);
    const userBots = [];

    for (const instanceId of instanceDirs) {
      try {
        const instanceDir = path.join(BOT_BASE_DIR, instanceId);
        const stats = await fs.stat(instanceDir);

        if (!stats.isDirectory()) continue;

        const configPath = path.join(instanceDir, 'config.json');
        if (!await fs.pathExists(configPath)) continue;

        const config = JSON.parse(await fs.readFile(configPath, 'utf8'));

        // Check if this bot belongs to the user
        if (config.bot_name && config.bot_name.startsWith(`${userId}-`)) {
          userBots.push(instanceId);
        }
      } catch (err) {
        console.error(`Error checking bot instance ${instanceId}:`, err);
      }
    }

    // Collect trades from all user's bots
    const allTrades = [];

    for (const instanceId of userBots) {
      try {
        const trades = await proxyFreqtradeApiRequest(instanceId, '/api/v1/trades');

        // Add bot identifier to each trade
        const tradesWithBotId = trades.trades.map(trade => ({
          ...trade,
          botId: instanceId
        }));

        allTrades.push(...tradesWithBotId);
      } catch (err) {
        console.error(`Error fetching trades for bot ${instanceId}:`, err);
      }
    }

    res.json({
      success: true,
      trades: allTrades,
      count: allTrades.length
    });
  } catch (error) {
    console.error(`Error fetching all user trades:`, error);
    res.status(500).json({
      success: false,
      message: `Failed to fetch trade data: ${error.message}`
    });
  }
});

// --- Routes ---
// Add token refresh endpoint - also apply rate limiting for security
app.post('/refresh-token', authLimiter, async (req, res) => {
  // This endpoint handles token refresh
  try {
    const { refreshTokenHandler } = require('./auth');
    await refreshTokenHandler(req, res);
  } catch (error) {
    console.error("Error in refresh token route:", error);
    return res.status(500).json({
      success: false,
      message: "Server error during token refresh"
    });
  }
});

// Public health check route - no authentication needed
app.get('/health', (req, res) => { res.json({ status: 'ok', timestamp: new Date().toISOString() }); });

// Token verification endpoint
app.get('/verify-token', authLimiter, async (req, res) => {
  try {
    console.log("Token verification requested");
    const authHeader = req.header('Authorization');

    if (!authHeader) {
      console.log("No Authorization header found");
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    // Only log presence of header, not its content
    console.log("Authorization header present:", !!authHeader);

    let token;
    if (authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    } else {
      token = authHeader;
    }

    if (!token) {
      return res.status(401).json({ success: false, message: 'Invalid token format' });
    }

    // First, attempt Firebase verification
    if (firebaseInitialized) {
      try {
        // Use the Firebase Admin SDK to verify the token
        const decodedFirebase = await admin.auth().verifyIdToken(token);
        console.log("Firebase verification successful for user:", decodedFirebase.uid);

        return res.json({
          success: true,
          valid: true,
          message: "Firebase token is valid",
          uid: decodedFirebase.uid,
          user: {
            id: decodedFirebase.uid,
            email: decodedFirebase.email,
            name: decodedFirebase.name,
            role: decodedFirebase.admin ? 'admin' : 'user'
          }
        });
      } catch (firebaseError) {
        console.log("Firebase verification failed with error type:", firebaseError.name);

        // If Firebase verification fails, try JWT
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          console.log("JWT verification successful for user:", decoded.user?.id || decoded.id);
          return res.json({
            success: true,
            valid: true,
            message: "JWT token is valid",
            user: decoded.user || decoded
          });
        } catch (jwtError) {
          console.error("JWT verification failed. Error type:", jwtError.name);
          return res.status(401).json({
            success: false,
            valid: false,
            message: "Invalid token",
            error: "Authentication failed"
          });
        }
      }
    } else {
      // Firebase not initialized, try JWT only
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        console.log("JWT verification successful for user:", decoded.user?.id || decoded.id);
        return res.json({
          success: true,
          valid: true,
          message: "JWT token is valid",
          user: decoded.user || decoded
        });
      } catch (jwtError) {
        console.error("JWT verification failed. Error type:", jwtError.name);
        return res.status(401).json({
          success: false,
          valid: false,
          message: "Invalid token",
          error: "Authentication failed"
        });
      }
    }
  } catch (error) {
    console.error("Token verification error:", error.name, error.message);
    return res.status(500).json({
      success: false,
      message: "Server error during token verification"
    });
  }
});

// Protected routes with authentication
app.get('/instances', authenticateToken, async (req, res) => {
  console.log("[Route /instances] Received GET request");
  try {
    const instanceData = [];
    const isAdmin = req.user.role === 'admin';
    const users = isAdmin ? await fs.readdir(BOT_BASE_DIR) : [req.user.id];
    for (const uid of users) {
      const userDir = path.join(BOT_BASE_DIR, uid);
      if (!await fs.pathExists(userDir)) continue;
      const instances = await fs.readdir(userDir);
      for (const instanceId of instances) {
        const instanceDir = path.join(userDir, instanceId);
        try {
          const stats = await fs.stat(instanceDir); if (!stats.isDirectory()) continue;
          const configPath = path.join(instanceDir, 'config.json'); const composePath = path.join(instanceDir, 'docker-compose.yml');
          if (!await fs.pathExists(configPath) || !await fs.pathExists(composePath)) continue;
          const config = JSON.parse(await fs.readFile(configPath, 'utf8')); const port = config.api_server?.listen_port || 'N/A';
          let status = 'stopped';
          try {
            const containerName = `freqtrade-${instanceId}`;
            const output = await runDockerCommand(['ps', '--filter', `name=^/${containerName}$`, '--format', '{{.Status}}']);
            if (output?.startsWith('Up')) status = 'running';
            else if (output) status = output.split(' ')[0].toLowerCase();
          } catch {
            status = 'unknown';
          }
          instanceData.push({ userId: uid, instanceId, port, status, strategy: config.strategy || 'N/A', exchange: config.exchange?.name || 'N/A', stakeCurrency: config.stake_currency || 'N/A', dryRun: config.dry_run !== undefined ? config.dry_run : 'N/A' });
        } catch (err) {
          instanceData.push({ userId: uid, instanceId, status: 'error', error: err.message });
        }
      }
    }
    res.json(instanceData);
  } catch (error) {
    console.error('[Route /instances] Error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

app.post('/provision', authenticateToken, async (req, res) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [Route /provision] ==========================================`);
  console.log(`[${timestamp}] [Route /provision] NEW PROVISIONING REQUEST RECEIVED`);
  console.log(`[${timestamp}] [Route /provision] ==========================================`);

  try {
    console.log(`[${timestamp}] [Route /provision] Request body:`, JSON.stringify(req.body, null, 2));
    console.log(`[${timestamp}] [Route /provision] User info:`, JSON.stringify({
      uid: req.user.uid,
      id: req.user.id,
      email: req.user.email
    }, null, 2));

    const { instanceId, port, apiUsername, apiPassword } = req.body;
    const userId = req.user.uid || req.user.id; // derive from authenticated token (prefer Firebase uid)

    console.log(`[${timestamp}] [Route /provision] Extracted parameters:`);
    console.log(`[${timestamp}] [Route /provision]   instanceId: ${instanceId}`);
    console.log(`[${timestamp}] [Route /provision]   port: ${port}`);
    console.log(`[${timestamp}] [Route /provision]   userId: ${userId}`);
    console.log(`[${timestamp}] [Route /provision]   apiUsername: ${apiUsername}`);
    console.log(`[${timestamp}] [Route /provision]   apiPassword: ${apiPassword ? '[PROVIDED]' : '[MISSING]'}`);

    // Validation
    console.log(`[${timestamp}] [Route /provision] Validating parameters...`);
    if (!instanceId || !port || !apiUsername || !apiPassword) {
      console.error(`[${timestamp}] [Route /provision] VALIDATION FAILED: Missing required parameters`);
      return res.status(400).json({ success: false, message: 'Missing params' });
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(instanceId)) {
      console.error(`[${timestamp}] [Route /provision] VALIDATION FAILED: Invalid instanceId format: ${instanceId}`);
      return res.status(400).json({ success: false, message: 'Invalid instanceId' });
    }

    if (typeof port !== 'number' || port < 1024 || port > 65535) {
      console.error(`[${timestamp}] [Route /provision] VALIDATION FAILED: Invalid port: ${port} (type: ${typeof port})`);
      return res.status(400).json({ success: false, message: 'Invalid port' });
    }

    console.log(`[${timestamp}] [Route /provision] ✓ Parameter validation passed`);

    // Directory setup
    console.log(`[${timestamp}] [Route /provision] Setting up directories...`);
    const userDir = path.join(BOT_BASE_DIR, userId);
    console.log(`[${timestamp}] [Route /provision] User directory: ${userDir}`);

    await fs.ensureDir(userDir);
    console.log(`[${timestamp}] [Route /provision] ✓ User directory ensured`);

    const instanceDir = path.join(userDir, instanceId);
    console.log(`[${timestamp}] [Route /provision] Instance directory: ${instanceDir}`);

    if (fs.existsSync(instanceDir)) {
      console.error(`[${timestamp}] [Route /provision] CONFLICT: Instance directory already exists: ${instanceDir}`);
      return res.status(409).json({ success: false, message: `Instance '${instanceId}' exists` });
    }

    console.log(`[${timestamp}] [Route /provision] ✓ Instance directory available`);

    // Queue the task
    console.log(`[${timestamp}] [Route /provision] Creating provisioning task...`);
    const task = { res, params: { instanceId, port, userId, apiUsername, apiPassword } };

    console.log(`[${timestamp}] [Route /provision] Adding task to queue...`);
    provisioningQueue.push(task);
    console.log(`[${timestamp}] [Route /provision] ✓ Task queued. Queue length: ${provisioningQueue.length}`);
    console.log(`[${timestamp}] [Route /provision] Current queue processing status: isProvisioning = ${isProvisioning}`);

    // Send immediate response
    if (task.res && !task.res.headersSent) {
      task.res.status(202).json({ success: true, message: 'Queued', instanceId, userId, timestamp });
      console.log(`[${timestamp}] [Route /provision] ✓ Sent 202 Accepted response to client`);
    } else {
      console.warn(`[${timestamp}] [Route /provision] WARNING: Headers already sent, cannot send 202 response`);
    }

    // Start processing if not already running
    if (!isProvisioning) {
      console.log(`[${timestamp}] [Route /provision] Starting queue processing...`);
      processProvisioningQueue();
    } else {
      console.log(`[${timestamp}] [Route /provision] Queue processing already in progress, task will be processed when current one completes`);
    }

    console.log(`[${timestamp}] [Route /provision] PROVISIONING REQUEST HANDLING COMPLETE`);
    console.log(`[${timestamp}] [Route /provision] ==========================================`);

  } catch (error) {
    console.error(`[${timestamp}] [Route /provision] ==========================================`);
    console.error(`[${timestamp}] [Route /provision] CRITICAL ERROR IN PROVISION ENDPOINT`);
    console.error(`[${timestamp}] [Route /provision] ==========================================`);
    console.error(`[${timestamp}] [Route /provision] Error:`, error);
    console.error(`[${timestamp}] [Route /provision] Error message:`, error.message);
    console.error(`[${timestamp}] [Route /provision] Error stack:`, error.stack);

    if (res && !res.headersSent) {
      res.status(500).json({
        success: false,
        message: 'Server error',
        error: error.message,
        timestamp: timestamp
      });
      console.log(`[${timestamp}] [Route /provision] Sent 500 error response to client`);
    } else {
      console.error(`[${timestamp}] [Route /provision] Cannot send error response - headers already sent`);
    }

    console.error(`[${timestamp}] [Route /provision] ==========================================`);
  }
});

app.put('/config/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params; console.log(`[Route /config/${instanceId}] Received PUT.`);
  try {
    const newConfigData = req.body; if (!newConfigData || Object.keys(newConfigData).length === 0) { return res.status(400).json({ success: false, message: 'Missing data' }); }
    const instanceDir = await resolveInstanceDir(instanceId); const configPath = path.join(instanceDir, 'config.json');
    if (!await fs.pathExists(configPath)) { return res.status(404).json({ success: false, message: `Not found` }); }
    const existingConfig = JSON.parse(await fs.readFile(configPath, 'utf8'));
    const protectedFields = ['api_server', 'db_url', 'logfile', 'bot_name'];
    for (const field of protectedFields) { if (newConfigData[field]) { console.warn(`Ignoring update to ${field}`); delete newConfigData[field]; } }
    if (newConfigData.exchange) { newConfigData.exchange = { ...existingConfig.exchange, ...newConfigData.exchange }; }
    if (newConfigData.pairlists) { existingConfig.pairlists = newConfigData.pairlists; delete newConfigData.pairlists; }
    if (newConfigData.strategy) { console.warn(`Strategy changed via config. Ensure file exists in instance dir.`); }
    const updatedConfig = { ...existingConfig, ...newConfigData };
    await fs.writeFile(configPath, JSON.stringify(updatedConfig, null, 2)); console.log(`[Route /config/${instanceId}] Updated.`);
    res.json({ success: true, message: 'Config updated. Restart may be needed.' });
  } catch (error) { console.error(`[Route /config/${instanceId}] Error:`, error); res.status(500).json({ success: false, message: 'Server error', error: error.message }); }
});

app.post('/start/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params;
  const containerName = `freqtrade-${instanceId}`;
  console.log(`[Route /start/${instanceId}] Received POST. Starting container ${containerName}`);
  try {
    await runDockerCommand(['start', containerName]);
    console.log(`[Route /start/${instanceId}] Started via docker start.`);
    return res.json({ success: true, message: `Started ${containerName}.` });
  } catch (error) {
    console.warn(`[Route /start/${instanceId}] Direct start failed:`, error.message);
    // If container doesn't exist, fallback to direct docker run
    if (error.message.includes('No such container')) {
      try {
        const instanceDir = await resolveInstanceDir(instanceId);
        // Load bot config
        const cfgPath = path.join(instanceDir, 'config.json');
        const config = JSON.parse(await fs.readFile(cfgPath, 'utf8'));
        const exchangeName = config.exchange.name;
        const hostSharedExchangeDataPath = path.join(SHARED_DATA_DIR, exchangeName);
        console.log(`[Route /start/${instanceId}] Container missing, running direct docker run fallback`);
        const userDataHostPath = path.join(instanceDir, 'user_data');
        const configHostPath = path.join(instanceDir, 'config.json');
        const entrypointHostPath = path.join(instanceDir, 'entrypoint.sh');

        // CRITICAL: Always use our enhanced entrypoint script
        await runDockerCommand([
          'run', '-d', '--name', containerName, '--network', 'host',
          '-v', `${entrypointHostPath}:/freqtrade/entrypoint.sh:ro`,
          '-v', `${userDataHostPath}:/freqtrade/user_data`,
          '-v', `${configHostPath}:/freqtrade/config.json:ro`,
          '-v', `${hostSharedExchangeDataPath}:/freqtrade/user_data/data/${exchangeName}:ro`,
          '-e', `DB_URL=${config.db_url}`,
          '-e', `LOGFILE=${config.logfile}`,
          '--entrypoint', '/freqtrade/entrypoint.sh',
          FREQTRADE_IMAGE
        ], instanceDir);
        console.log(`[${instanceId}] Container started via direct docker run fallback.`);
        return res.json({ success: true, message: `Provisioned and started ${containerName}.` });
      } catch (fallbackErr) {
        console.error(`[Route /start/${instanceId}] Direct run fallback failed:`, fallbackErr);
        return res.status(500).json({ success: false, message: `Failed start via direct run`, error: fallbackErr.message });
      }
    }
    // Other errors
    console.error(`[Route /start/${instanceId}] Error:`, error);
    res.status(500).json({ success: false, message: `Failed start.`, error: error.message });
  }
});

app.post('/stop/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params;
  const containerName = `freqtrade-${instanceId}`;
  console.log(`[Route /stop/${instanceId}] Received POST. Stopping container ${containerName}`);
  try {
    await runDockerCommand(['stop', containerName]);
    console.log(`[Route /stop/${instanceId}] Stopped.`);
    res.json({ success: true, message: `Stopped ${containerName}.` });
  } catch (error) {
    console.error(`[Route /stop/${instanceId}] Error:`, error);
    const message = error.message?.includes("No such container") ? `Container not found.` : `Failed stop.`;
    res.status(500).json({ success: false, message, error: error.message });
  }
});

app.post('/restart/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params;
  const containerName = `freqtrade-${instanceId}`;
  console.log(`[Route /restart/${instanceId}] Received POST. Restarting container ${containerName}`);
  try {
    await runDockerCommand(['restart', containerName]);
    console.log(`[Route /restart/${instanceId}] Restarted.`);
    res.json({ success: true, message: `Restarted ${containerName}.` });
  } catch (error) {
    console.error(`[Route /restart/${instanceId}] Error:`, error);
    res.status(500).json({ success: false, message: `Failed restart.`, error: error.message });
  }
});

app.delete('/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params; console.log(`[Route /delete/${instanceId}] Received DELETE.`);
  const instanceDir = await resolveInstanceDir(instanceId); const composePath = path.join(instanceDir, 'docker-compose.yml');
  try {
    if (!await fs.pathExists(instanceDir)) { return res.status(404).json({ success: false, message: `Not found.` }); }
    if (await fs.pathExists(composePath)) {
      console.log(`Deleting container via 'docker-compose down'...`);
      await runDockerCommand(['-f', composePath, 'down', '--remove-orphans', '-v'], instanceDir, 'docker-compose');
      console.log(`Container deleted via 'docker-compose'.`);
    }
    else { console.warn(`Compose file missing.`); }
    console.log(`Removing dir: ${instanceDir}`);
    // TURSO: delete remote DB if exists
    if (TURSO_API_KEY && TURSO_ORG) {
      const tursoName = `bot_${instanceId.split('_')[0]}_${instanceId}`; // assumes userId in instanceId, alternatively track it in config
      console.log(`[${instanceId}] Deleting Turso DB '${tursoName}'...`);
      try {
        await runDockerCommand(['db', 'delete', tursoName, '--org', TURSO_ORG, '--api-key', TURSO_API_KEY, '--force'], null, 'turso');
        console.log(`[${instanceId}] Turso DB deleted.`);
      } catch (err) {
        console.error(`[${instanceId}] Failed deleting Turso DB:`, err.message);
      }
    }
    await fs.remove(instanceDir);
    console.log(`Dir removed.`);
    res.json({ success: true, message: `Deleted.` });
  } catch (error) { console.error(`[Route /delete/${instanceId}] Error:`, error); res.status(500).json({ success: false, message: `Failed delete.`, error: error.message }); }
});

app.get('/status/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params; const instanceDir = await resolveInstanceDir(instanceId);
  try {
    if (!await fs.pathExists(instanceDir)) { return res.status(404).json({ success: false, message: `Not found.` }); }
    let status = 'stopped'; let statusDetail = '';
    try { const c = `freqtrade-${instanceId}`; statusDetail = await runDockerCommand(['ps', '--filter', `name=^/${c}$`, '--format', '{{.Status}}']); if (statusDetail?.startsWith('Up')) status = 'running'; else if (statusDetail) status = statusDetail.split(' ')[0].toLowerCase(); }
    catch (e) { status = 'unknown'; statusDetail = e.message; } res.json({ instanceId, status, statusDetail });
  } catch (error) { console.error(`[Route /status/${instanceId}] Error:`, error); res.status(500).json({ success: false, message: `Failed status check.`, error: error.message }); }
});

app.get('/strategies', authenticateToken, async (req, res) => {
  console.log("[Route /strategies] GET (listing from SHARED dir)."); // Note: Still uses SHARED dir
  try {
    if (!await fs.pathExists(STRATEGIES_DIR)) { return res.status(500).json({ success: false, message: `Shared dir not found: ${STRATEGIES_DIR}` }); }
    const files = await fs.readdir(STRATEGIES_DIR); const strategies = files.filter(f => f.endsWith('.py') && !f.startsWith('_')).map(f => f.replace('.py', ''));
    res.json({ success: true, strategies });
  } catch (error) { console.error('[Route /strategies] Error:', error); res.status(500).json({ success: false, message: 'Server error', error: error.message }); }
});

app.put('/strategy/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params; const { strategy } = req.body; console.log(`[Route /strategy/${instanceId}] PUT strategy '${strategy}' (config only).`);
  try {
    if (!strategy) { return res.status(400).json({ success: false, message: 'Strategy name required' }); }
    const instanceDir = await resolveInstanceDir(instanceId); const configPath = path.join(instanceDir, 'config.json'); const composePath = path.join(instanceDir, 'docker-compose.yml');
    if (!await fs.pathExists(configPath)) { return res.status(404).json({ success: false, message: `Not found.` }); }
    // Define instanceStrategiesDir for the warning message
    const userDataDir = path.join(instanceDir, 'user_data');
    const instanceStrategiesDir = path.join(userDataDir, 'strategies');

    const config = JSON.parse(await fs.readFile(configPath, 'utf8')); config.strategy = strategy;
    await fs.writeFile(configPath, JSON.stringify(config, null, 2)); console.log(`Config updated.`);
    if (!await fs.pathExists(composePath)) { console.warn(`Compose missing, cannot restart.`); return res.status(200).json({ success: true, message: `Strategy updated, restart failed (no compose). File not copied.` }); }
    console.log(`Restarting container (using existing strategy file)...`); console.warn(`Ensure '${strategy}.py' exists in '${instanceStrategiesDir}'. NOT copied by this endpoint.`);
    await runDockerCommand(['restart'], instanceDir, 'docker-compose'); console.log(`Restarted (attempting ${strategy}).`);
    res.json({ success: true, message: `Strategy updated & bot restarted. File NOT automatically updated.` });
  } catch (error) { console.error(`[Route /strategy/${instanceId}] Error:`, error); res.status(500).json({ success: false, message: `Failed update/restart.`, error: error.message }); }
});

app.get('/logs/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  const { instanceId } = req.params; const lines = req.query.lines && /^\d+$/.test(req.query.lines) ? req.query.lines : '100';
  console.log(`[Route /logs/${instanceId}] GET (lines: ${lines}).`);
  const instanceDir = await resolveInstanceDir(instanceId); const c = `freqtrade-${instanceId}`;
  try {
    if (!await fs.pathExists(instanceDir)) { return res.status(404).json({ success: false, message: `Not found.` }); }
    const logs = await runDockerCommand(['logs', '--tail', lines, c]); console.log(`Retrieved logs.`);
    res.json({ success: true, logs: logs || "No logs found." });
  } catch (error) { console.error(`[Route /logs/${instanceId}] Error:`, error); const m = error.message?.includes("No such container") ? `Container '${c}' not found.` : `Failed get logs.`; res.status(500).json({ success: false, message: m, error: error.message }); }
});

// --- Server Start ---
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`=================================================`);
  console.log(` Freqtrade Bot Manager (Shared Data Provision)`); // Updated Banner
  console.log(`-------------------------------------------------`);
  console.log(` Service Listening on: http://0.0.0.0:${PORT}`);
  console.log(` Bot Instance Base Dir: ${BOT_BASE_DIR}`);
  console.log(` Main Strategies Source: ${MAIN_STRATEGIES_SOURCE_DIR}`);
  console.log(` SHARED Data Directory: ${SHARED_DATA_DIR}`); // Added Shared Data Dir
  console.log(` Host Freqtrade Needed: NO (for provisioning) / YES (for managing shared data)`); // Clarified host need
  console.log(`=================================================`);
});
// Graceful shutdown
process.on('SIGTERM', () => { console.log('SIGTERM: closing server'); server.close(() => { console.log('Server closed'); process.exit(0); }); setTimeout(() => process.exit(1), 10000); });
process.on('SIGINT', () => { console.log('SIGINT: closing server'); server.close(() => { console.log('Server closed'); process.exit(0); }); setTimeout(() => process.exit(1), 10000); });