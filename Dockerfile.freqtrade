# Dockerfile.freqtrade
FROM freqtradeorg/freqtrade:stable
# Install the libsql dialect so SQLAlchemy accepts sqlite+libsql://…
RUN pip install sqlalchemy-libsql
# Copy the sitecustomize patch from bot-manager directory
COPY sitecustomize.py /usr/local/lib/python3.12/site-packages/sitecustomize.py
# Override persistence migrations to ensure schema created
COPY freqtrade/freqtrade/persistence/migrations.py /freqtrade/freqtrade/persistence/migrations.py
# CRITICAL: Replace the actual key_value_store.py file with our patched version
COPY freqtrade/freqtrade/persistence/key_value_store.py /freqtrade/freqtrade/persistence/key_value_store.py
# CRITICAL: Replace the entrypoint script with our enhanced version
COPY entrypoint.sh /freqtrade/entrypoint.sh