
# --- Do not remove this comment ---
# freqtrade backtesting: Initializi Freqtrade framework...

# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# isort: skip_file
# --- Do not remove this comment ---
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter


class DefaultStrategy(IStrategy):
    """
    Default Strategy provided by Bot Manager
    """
    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.
    INTERFACE_VERSION = 3  # Use the latest appropriate interface version

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.10  # 10% ROI after 0 minutes (example)
    }

    # Optimal stoploss calculation method.
    stoploss = -0.10  # 10% stoploss (example)

    # Trailing stoploss
    trailing_stop = False

    # Optimal timeframe for the strategy.
    timeframe = '5m' # Example timeframe

    # Run "populate_indicators()" only for new candle.
    process_only_new_candles = True

    # These values can be overridden in the config.
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 20 # Example

    # Optional order type mapping.
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'gtc',
        'exit': 'gtc'
    }

    # Hyperopt parameters (example)
    buy_rsi = IntParameter(10, 40, default=30, space='buy')
    sell_rsi = IntParameter(60, 90, default=70, space='sell')

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Example indicator: RSI
        dataframe['rsi'] = ta.RSI(dataframe)
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Example entry signal: RSI crosses below buy_rsi value
        dataframe.loc[
            (qtpylib.crossed_below(dataframe['rsi'], self.buy_rsi.value)),
            'enter_long'] = 1
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Example exit signal: RSI crosses above sell_rsi value
        dataframe.loc[
            (qtpylib.crossed_above(dataframe['rsi'], self.sell_rsi.value)),
            'exit_long'] = 1
        return dataframe

