# Patch SQLAlchemy create_engine to use NullPool for libsql connections
import os, importlib, sys
from sqlalchemy.pool import NullPool
from sqlalchemy import create_engine as _orig_create_engine
import sqlalchemy

# adjust Python path to include Freqtrade code
sys.path.insert(0, '/freqtrade')
sys.path.insert(1, '/freqtrade/freqtrade')

def create_engine(*args, **kwargs):
    url = args[0] if args else kwargs.get('url', '')
    if isinstance(url, str) and url.startswith(('sqlite+libsql://', 'libsql://')):
        kwargs.setdefault('poolclass', NullPool)
    return _orig_create_engine(*args, **kwargs)
sqlalchemy.create_engine = create_engine

# No-op migrations
try:
    migs = importlib.import_module('freqtrade.persistence.migrations')
    migs.check_migrate = lambda engine, decl_base, previous_tables: None
except Exception:
    pass

# Fallback get_trades_proxy
try:
    tm = importlib.import_module('freqtrade.persistence.trade_model')
    orig = tm.Trade.get_trades_proxy
    import sqlite3
    from sqlalchemy.exc import OperationalError as SAOperationalError
    def safe_get_trades_proxy(*args, **kwargs):
        try:
            return orig(*args, **kwargs)
        except (sqlite3.OperationalError, SAOperationalError):
            return []
    tm.Trade.get_trades_proxy = safe_get_trades_proxy
    print('Patched Trade.get_trades_proxy to catch missing table errors')
except Exception:
    pass

# Auto-init schema
try:
    models = importlib.import_module('freqtrade.persistence.models')
    init_db = models.init_db
    db_url = os.getenv('DB_URL')
    if db_url:
        init_db(db_url)
        print(f"Initialized schema for {db_url}")
except Exception as e:
    print(f"Failed schema init: {e}")

import importlib
try:
    wmod = importlib.import_module('freqtrade.worker')
    Worker = getattr(wmod, 'Worker', None)
    if Worker:
        orig_init = Worker._init
        def new_init(self, fresh):
            from freqtrade.persistence.models import init_db
            db_url = getattr(self, 'config', {}).get('db_url')
            if db_url:
                init_db(db_url)
            return orig_init(self, fresh)
        Worker._init = new_init
        print('Patched Worker._init to auto-init DB')
except Exception:
    pass

# Monkey-patch Wallets._update_dry to swallow missing-table errors
try:
    wmod = importlib.import_module('freqtrade.wallets')
    Wallets = getattr(wmod, 'Wallets', None)
    if Wallets:
        orig_update_dry = Wallets._update_dry
        def safe_update_dry(self, *args, **kwargs):
            try:
                return orig_update_dry(self, *args, **kwargs)
            except Exception:
                return None
        Wallets._update_dry = safe_update_dry
        print('Patched Wallets._update_dry to ignore missing trades table')
except Exception:
    pass

# Patch KeyValueStore.get_value to swallow missing table errors
try:
    import freqtrade.persistence.key_value_store as _kvs
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr
    _orig_kv_get = _kvs.KeyValueStore.get_value
    def safe_get_value(key):
        try:
            return _orig_kv_get(key)
        except (_SQLOpErr, _SAOpErr):
            return None
    _kvs.KeyValueStore.get_value = safe_get_value
    print('Patched KeyValueStore.get_value to ignore missing table errors')
except Exception:
    pass

# Patch set_startup_time to ignore missing table errors - Multiple approaches
try:
    import freqtrade.persistence.key_value_store as _kvs
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Approach 1: Patch the module function directly
    _orig_set_startup_time = _kvs.set_startup_time
    def safe_set_startup_time():
        try:
            return _orig_set_startup_time()
        except (_SQLOpErr, _SAOpErr) as e:
            print(f'Patched set_startup_time: ignoring missing trades table error: {e}')
            return None
    _kvs.set_startup_time = safe_set_startup_time

    # Approach 2: Also patch it in the global namespace for imports
    import sys
    if 'freqtrade.persistence.key_value_store' in sys.modules:
        sys.modules['freqtrade.persistence.key_value_store'].set_startup_time = safe_set_startup_time

    print('Patched set_startup_time to ignore missing table errors')
except Exception as e:
    print(f'Failed to patch set_startup_time: {e}')

# Additional patch: Intercept FreqTradeBot.startup() to ensure DB is ready
try:
    import freqtrade.freqtradebot as _ftbot
    _orig_startup = _ftbot.FreqtradeBot.startup
    def safe_startup(self):
        try:
            # Ensure database is initialized before calling original startup
            from freqtrade.persistence.models import init_db
            if hasattr(self, 'config') and 'db_url' in self.config:
                init_db(self.config['db_url'])
                print('FreqTradeBot.startup: Re-initialized database before startup')
            return _orig_startup(self)
        except Exception as e:
            print(f'FreqTradeBot.startup: Error during startup: {e}')
            # Try to continue anyway
            return _orig_startup(self)
    _ftbot.FreqtradeBot.startup = safe_startup
    print('Patched FreqTradeBot.startup to ensure DB initialization')
except Exception as e:
    print(f'Failed to patch FreqTradeBot.startup: {e}')

# Critical patch: Replace set_startup_time import in freqtradebot module
try:
    import freqtrade.freqtradebot as _ftbot
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Create a safe version of set_startup_time
    def safe_set_startup_time_global():
        try:
            from freqtrade.persistence.key_value_store import set_startup_time as _orig_func
            return _orig_func()
        except (_SQLOpErr, _SAOpErr) as e:
            print(f'CRITICAL PATCH: set_startup_time failed with missing table, ignoring: {e}')
            return None
        except Exception as e:
            print(f'CRITICAL PATCH: set_startup_time failed with unexpected error: {e}')
            return None

    # Replace the function in the freqtradebot module
    _ftbot.set_startup_time = safe_set_startup_time_global
    print('CRITICAL PATCH: Replaced set_startup_time in freqtradebot module')
except Exception as e:
    print(f'Failed to apply critical set_startup_time patch: {e}')

# Ultimate patch: Monkey-patch the import mechanism itself
try:
    import sys
    import importlib.util

    # Store original import function
    _orig_import = __builtins__.__import__

    def patched_import(name, globals=None, locals=None, fromlist=(), level=0):
        # Call original import
        module = _orig_import(name, globals, locals, fromlist, level)

        # If importing set_startup_time from key_value_store, patch it
        if (name == 'freqtrade.persistence.key_value_store' and
            fromlist and 'set_startup_time' in fromlist):

            from sqlite3 import OperationalError as _SQLOpErr
            from sqlalchemy.exc import OperationalError as _SAOpErr

            # Get the original function
            orig_func = getattr(module, 'set_startup_time')

            def safe_set_startup_time_import():
                try:
                    return orig_func()
                except (_SQLOpErr, _SAOpErr) as e:
                    print(f'IMPORT PATCH: set_startup_time failed with missing table, ignoring: {e}')
                    return None
                except Exception as e:
                    print(f'IMPORT PATCH: set_startup_time failed with unexpected error: {e}')
                    return None

            # Replace the function in the module
            setattr(module, 'set_startup_time', safe_set_startup_time_import)
            print('IMPORT PATCH: Patched set_startup_time during import')

        return module

    # Replace the import function
    __builtins__.__import__ = patched_import
    print('ULTIMATE PATCH: Installed import hook for set_startup_time')

except Exception as e:
    print(f'Failed to install import hook: {e}')
