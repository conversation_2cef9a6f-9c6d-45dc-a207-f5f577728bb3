# This is a patch for the startup_backpopulate_precision function in freqtradebot.py
# It replaces the problematic function with a safe version that handles missing database tables

def startup_backpopulate_precision(self) -> None:
    """
    PATCHED VERSION: Safe startup_backpopulate_precision that handles missing database tables
    """
    try:
        from sqlite3 import OperationalError as SQLOpErr
        from sqlalchemy.exc import OperationalError as SAOpErr
        
        print('FREQTRADEBOT PATCH: startup_backpopulate_precision: Starting with error handling')
        
        # Try to get trades, but handle missing table gracefully
        try:
            trades = Trade.get_trades([Trade.contract_size.is_(None)])
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Found {len(trades)} trades to process')
        except (<PERSON><PERSON><PERSON><PERSON><PERSON>rr, SAOpErr) as e:
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Database error (skipping): {e}')
            # If the trades table doesn't exist, just skip this function
            return
        except Exception as e:
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Unexpected error (skipping): {e}')
            return
        
        # Process trades if we got them successfully
        for trade in trades:
            try:
                if trade.exchange != self.exchange.id:
                    continue
                trade.precision_mode = self.exchange.precisionMode
                trade.precision_mode_price = self.exchange.precision_mode_price
                trade.amount_precision = self.exchange.get_precision_amount(trade.pair)
                trade.price_precision = self.exchange.get_precision_price(trade.pair)
                trade.contract_size = self.exchange.get_contract_size(trade.pair)
            except Exception as trade_e:
                print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Error processing trade {trade}: {trade_e}')
                continue
        
        # Try to commit changes
        try:
            Trade.commit()
            print('FREQTRADEBOT PATCH: startup_backpopulate_precision: Successfully committed changes')
        except (SQLOpErr, SAOpErr) as e:
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Commit failed (ignoring): {e}')
        except Exception as e:
            print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Unexpected commit error (ignoring): {e}')
            
    except Exception as e:
        print(f'FREQTRADEBOT PATCH: startup_backpopulate_precision: Critical error (ignoring): {e}')
        # If anything goes wrong, just skip this function entirely
        return
